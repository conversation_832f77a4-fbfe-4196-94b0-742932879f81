#!/bin/bash

# Langfuse 中文国际化开发环境启动脚本
# Development environment startup script for Langfuse Chinese i18n

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${CYAN}🚀 启动 Langfuse 中文国际化开发环境${NC}"
echo -e "${CYAN}🚀 Starting Langfuse Chinese i18n Development Environment${NC}"
echo "=================================="

# 1. 检查 Docker 是否运行
check_docker() {
    log_info "检查 Docker 状态..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 运行正常"
}

# 2. 启动基础服务 (PostgreSQL, ClickHouse, Redis, MinIO)
start_services() {
    log_info "启动基础服务..."
    
    # 停止可能存在的服务
    docker-compose down > /dev/null 2>&1 || true
    
    # 启动服务
    log_info "启动 PostgreSQL, ClickHouse, Redis, MinIO..."
    docker-compose up -d postgres clickhouse redis minio
    
    log_success "基础服务启动完成"
}

# 3. 等待服务就绪
wait_for_services() {
    log_info "等待服务启动完成..."
    
    # 等待 PostgreSQL
    log_info "等待 PostgreSQL..."
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            log_success "PostgreSQL 已就绪"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    # 等待 ClickHouse
    log_info "等待 ClickHouse..."
    for i in {1..30}; do
        if curl -s http://localhost:8123/ping > /dev/null 2>&1; then
            log_success "ClickHouse 已就绪"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    # 等待 Redis
    log_info "等待 Redis..."
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            log_success "Redis 已就绪"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    log_success "所有服务已就绪"
}

# 4. 设置环境变量
setup_environment() {
    log_info "设置开发环境变量..."
    
    # 创建 .env 文件
    cat > .env << EOF
# Langfuse 开发环境配置
# Development environment configuration

# 基础配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dev-secret-for-local-development-only
SALT=dev-salt-for-local-development-only-change-in-production

# 数据库配置
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres
DIRECT_URL=postgresql://postgres:postgres@localhost:5432/postgres

# ClickHouse 配置
CLICKHOUSE_MIGRATION_URL=clickhouse://localhost:9000
CLICKHOUSE_URL=http://localhost:8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=default
CLICKHOUSE_CLUSTER_ENABLED=false

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_AUTH=myredissecret
REDIS_TLS_ENABLED=false

# S3/MinIO 配置
LANGFUSE_S3_EVENT_UPLOAD_BUCKET=langfuse
LANGFUSE_S3_EVENT_UPLOAD_REGION=auto
LANGFUSE_S3_EVENT_UPLOAD_ACCESS_KEY_ID=minio
LANGFUSE_S3_EVENT_UPLOAD_SECRET_ACCESS_KEY=miniosecret
LANGFUSE_S3_EVENT_UPLOAD_ENDPOINT=http://localhost:9090
LANGFUSE_S3_EVENT_UPLOAD_FORCE_PATH_STYLE=true
LANGFUSE_S3_EVENT_UPLOAD_PREFIX=events/

LANGFUSE_S3_MEDIA_UPLOAD_BUCKET=langfuse
LANGFUSE_S3_MEDIA_UPLOAD_REGION=auto
LANGFUSE_S3_MEDIA_UPLOAD_ACCESS_KEY_ID=minio
LANGFUSE_S3_MEDIA_UPLOAD_SECRET_ACCESS_KEY=miniosecret
LANGFUSE_S3_MEDIA_UPLOAD_ENDPOINT=http://localhost:9090
LANGFUSE_S3_MEDIA_UPLOAD_FORCE_PATH_STYLE=true
LANGFUSE_S3_MEDIA_UPLOAD_PREFIX=media/

# 功能开关
LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES=true
TELEMETRY_ENABLED=false

# 开发模式设置
NODE_ENV=development
NEXT_PUBLIC_LANGFUSE_CLOUD_REGION=DEV

# 初始化配置（可选）
LANGFUSE_INIT_ORG_NAME=开发组织
LANGFUSE_INIT_PROJECT_NAME=中文测试项目
LANGFUSE_INIT_USER_EMAIL=<EMAIL>
LANGFUSE_INIT_USER_NAME=管理员
LANGFUSE_INIT_USER_PASSWORD=admin123456
EOF
    
    log_success "环境变量配置完成"
}

# 5. 安装依赖
install_dependencies() {
    log_info "检查并安装项目依赖..."
    
    if ! command -v pnpm &> /dev/null; then
        log_info "安装 pnpm..."
        npm install -g pnpm
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ] || [ ! -d "web/node_modules" ]; then
        log_info "安装项目依赖..."
        pnpm install
    else
        log_info "依赖已存在，跳过安装"
    fi
    
    log_success "依赖检查完成"
}

# 6. 数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待一下确保数据库完全启动
    sleep 5
    
    # 生成 Prisma 客户端
    pnpm db:generate --filter=@langfuse/shared || log_warning "Prisma 客户端生成失败，可能需要手动运行"
    
    # 运行数据库迁移
    pnpm db:migrate --filter=@langfuse/shared || log_warning "数据库迁移失败，可能需要手动运行"
    
    log_success "数据库迁移完成"
}

# 7. 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    echo ""
    log_success "🎉 开发环境启动完成！"
    echo ""
    echo -e "${CYAN}📋 访问链接:${NC}"
    echo -e "  🌐 英文版: ${GREEN}http://localhost:3000${NC}"
    echo -e "  🌐 中文版: ${GREEN}http://localhost:3000/zh${NC}"
    echo -e "  🧪 测试页面: ${GREEN}http://localhost:3000/test-i18n${NC}"
    echo -e "  📊 MinIO 控制台: ${GREEN}http://localhost:9091${NC} (用户名: minio, 密码: miniosecret)"
    echo ""
    echo -e "${YELLOW}💡 提示: 使用 Ctrl+C 停止开发服务器${NC}"
    echo ""
    
    # 启动开发服务器
    cd web && pnpm dev
}

# 8. 清理函数
cleanup() {
    log_info "正在停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 捕获退出信号
trap cleanup EXIT

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_docker
            start_services
            wait_for_services
            setup_environment
            install_dependencies
            run_migrations
            start_dev_server
            ;;
        "stop")
            log_info "停止所有服务..."
            docker-compose down
            log_success "所有服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose down
            sleep 2
            main start
            ;;
        "status")
            log_info "服务状态:"
            docker-compose ps
            ;;
        "logs")
            log_info "显示服务日志:"
            docker-compose logs --tail=50
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动开发环境 (默认)"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 显示服务状态"
            echo "  logs    - 显示服务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
